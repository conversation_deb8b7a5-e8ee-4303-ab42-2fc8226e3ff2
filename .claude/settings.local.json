{"permissions": {"allow": ["Bash(rg:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(rg -n \"PostMapping.*upload\" D:/projects/blcjrone/src --type java)", "Bash(git add:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(curl:*)", "Bash(git commit:*)", "mcp__cjrone-postgres__query", "mcp__yuyao-postgres__query", "WebFetch(domain:docs.anthropic.com)", "Bash(mvn compile:*)", "Bash(cmd.exe /c \"cd /d D:\\projects\\blcjrone && mvn compile -q\")", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "WebSearch"], "deny": [], "additionalDirectories": ["D:/projects"]}, "outputStyle": "default"}