# Repository Guidelines

## Project Structure & Module Organization
- `src/main/java/com/hmit/kernespring/modules/*/(controller|service|service/impl|dao|entity)`: Java source by domain. Example: `modules/sys/controller/SysUserController.java`.
- `src/main/resources`: app config and assets. Key paths: `application-*.yml`, `mapper/**.xml` (MyBatis‑Plus), `static/swagger`, `static/pdf/templates`.
- `src/test/java`: unit/integration tests (e.g., `JwtTest.java`, `PdfTest.java`).
- `lib`: vendored JARs required at build/runtime (e.g., `OpenMasClient-1.2.jar`).
- `target`: build outputs (e.g., `target/cjrone.jar`).

## Build, Test, and Development Commands
- Build jar: `mvn clean package` (tests are skipped by default via Surefire).
- Run tests: `mvn -DskipTests=false test` or include on package: `mvn clean package -DskipTests=false`.
- Run locally (dev):
  - Maven: `mvn spring-boot:run -Dspring-boot.run.profiles=dev`
  - Jar: `java -jar target/cjrone.jar --spring.profiles.active=dev`
- Docker:
  - Maven image: `mvn clean package docker:build` (image `cjrone/fast`).
  - Manual: ensure `target/cjrone.jar` then `docker build -t cjrone/hm .` and `docker-compose up -d`.

## Coding Style & Naming Conventions
- Java 8, 4‑space indentation, UTF‑8. Prefer Lombok for boilerplate where used.
- Suffixes: `*Controller`, `*Service`/`*ServiceImpl`, `*Dao`, `*Entity`.
- Package by domain under `com.hmit.kernespring.modules.<area>`.
- MyBatis mapper XML lives in `src/main/resources/mapper/**`. Keep SQL readable and parameterized.

## Testing Guidelines
- Framework: Spring Boot Test (JUnit). Tests in `src/test/java`, named `*Test.java`.
- Enable tests explicitly (default skip): `mvn -DskipTests=false test`.
- Aim to cover service logic and critical controllers; provide sample test data or mocks where DB is required.

## Commit & Pull Request Guidelines
- Commits: short, present‑tense summaries; Chinese or English is fine. Reference the module or feature (e.g., “青少年补助 上限”, “sys: 登录权限修复”). Link issues when applicable (`Closes #123`).
- PRs: include description, linked issues, steps to verify, and notes for schema/config changes. Attach logs or screenshots for API/printing features when helpful.

## Security & Configuration Tips
- Use profiles: `dev`, `test`, `prod` via `spring.profiles.active`.
- Keep secrets and DB credentials in environment or private config; do not commit sensitive values in `application-*.yml`.
- Ensure required vendor JARs in `lib/` are available before packaging or building Docker images.

