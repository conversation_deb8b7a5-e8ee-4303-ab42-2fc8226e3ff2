<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.matter_application.dao.DataDisabilityCertificateImportRecordDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.matter_application.entity.DataDisabilityCertificateImportRecordEntity" id="dataDisabilityCertificateImportRecordMap">
        <result property="id" column="id"/>
        <result property="importBatchNo" column="import_batch_no"/>
        <result property="originalFilename" column="original_filename"/>
        <result property="filePath" column="file_path"/>
        <result property="sysOssId" column="sys_oss_id"/>
        <result property="importUserId" column="import_user_id"/>
        <result property="importTime" column="import_time"/>
        <result property="originalDataId" column="original_data_id"/>
        <result property="rowNumber" column="row_number"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="sex" column="sex"/>
        <result property="nationality" column="nationality"/>
        <result property="educationDegree" column="education_degree"/>
        <result property="maritalStatus" column="marital_status"/>
        <result property="accountType" column="account_type"/>
        <result property="telphone" column="telphone"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="jiedao" column="jiedao"/>
        <result property="shequ" column="shequ"/>
        <result property="nativePlace" column="native_place"/>
        <result property="livePlace" column="live_place"/>
        <result property="guardianName" column="guardian_name"/>
        <result property="guardianRelation" column="guardian_relation"/>
        <result property="guardianTelephone" column="guardian_telephone"/>
        <result property="guardianMobile" column="guardian_mobile"/>
        <result property="disableId" column="disable_id"/>
        <result property="disabilityCategory" column="disability_category"/>
        <result property="disabilityDegree" column="disability_degree"/>
        <result property="disabilityInfo" column="disability_info"/>
        <result property="completeTime" column="complete_time"/>
        <result property="status" column="status"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="birthday" column="birthday"/>
        <result property="postalCode" column="postal_code"/>
        <result property="contactPersonIdCard" column="contact_person_id_card"/>
        <result property="medicalInsuranceStatus" column="medical_insurance_status"/>
        <result property="diagnosisCertificateStartTime" column="diagnosis_certificate_start_time"/>
        <result property="diagnosisCertificateEndTime" column="diagnosis_certificate_end_time"/>
        <result property="diagnosisCertificateImage" column="diagnosis_certificate_image"/>
        <result property="yanglaoInsuranceStatus" column="yanglao_insurance_status"/>
        <result property="familyEconomicCondition" column="family_economic_condition"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.matter_application.entity.DataDisabilityCertificateImportRecordEntity">
		SELECT * FROM data_disability_certificate_import_record order by id desc
	</select>

</mapper>