<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.data_management.dao.DataDisabilityCertificateDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity" id="dataDisabilityCertificateMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="sex" column="sex"/>
        <result property="nationality" column="nationality"/>
        <result property="educationDegree" column="education_degree"/>
        <result property="maritalStatus" column="marital_status"/>
        <result property="accountType" column="account_type"/>
        <result property="telphone" column="telphone"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="jiedao" column="jiedao"/>
        <result property="shequ" column="shequ"/>
        <result property="nativePlace" column="native_place"/>
        <result property="livePlace" column="live_place"/>
        <result property="guardianName" column="guardian_name"/>
        <result property="guardianRelation" column="guardian_relation"/>
        <result property="guardianTelephone" column="guardian_telephone"/>
        <result property="guardianMobile" column="guardian_mobile"/>
        <result property="disableId" column="disable_id"/>
        <result property="disabilityCategory" column="disability_category"/>
        <result property="disabilityDegree" column="disability_degree"/>
        <result property="disabilityInfo" column="disability_info"/>
        <result property="completeTime" column="complete_time"/>
        <result property="status" column="status"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity">
		SELECT * FROM data_disability_certificate where 1=1
        <if test="idCard != null and idCard.trim() != ''">
            and id_card = #{idCard}
        </if>
		 order by id desc

	</select>
    <select id="queryListDataByMap" resultType="com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity">
        SELECT a.id,a.name,a.id_card,a.disable_id,a.sex,b.disability_category,b.disability_degree,a.guardian_name,a.guardian_phone,a.family_econo_condition,a.medical_insurance,
        a.rehabilitation_subsidy,bus_card,nursing_subsidy,living_allowance,child_education_subsidy,resident_pension_insurance,
        individual_pension_insurance,a.status,a.create_id,a.create_time FROM data_disability_certificate a LEFT JOIN  cjrone_disability_hospital b ON a.id_card = b.id_card
        where 1=1
        <if test="status != null and status.trim() != ''">
            and a.status = #{status}
        </if>
        <if test="name != null and name.trim() != ''">
            and a.name = #{name}
        </if>
        <if test="idCard != null and idCard.trim() != ''">
            and a.id_card = #{idCard}
        </if>
        order by a.create_time desc
        <if test="offset != null and limit != null">
            limit #{limit} offset #{offset}
        </if>
	</select>
    <select id="updateOthersADisableIdByMap" parameterType="map">
		update cjrone_living_allowance set disable_id = #{disableId} where id_card = #{idCard};
	</select>
    <select id="updateOthersBDisableIdByMap" parameterType="map">
		update cjrone_love_bus_card set disable_id = #{disableId} where id_card = #{idCard};
	</select>
    <select id="updateOthersCDisableIdByMap" parameterType="map">
		update cjrone_nursing_subsidy set disable_id = #{disableId} where id_card = #{idCard};
	</select>
    <select id="updateOthersDDisableIdByMap" parameterType="map">
		update cjrone_rehabilitation_subsidy set disable_id = #{disableId} where id_card = #{idCard};
	</select>
    <select id="updateOthersDisableIdByMap" parameterType="map">
		update cjrone_living_allowance set disable_id = #{disableId} where id_card = #{idCard};
		update cjrone_love_bus_card set disable_id = #{disableId} where id_card = #{idCard};
		update cjrone_nursing_subsidy set disable_id = #{disableId} where id_card = #{idCard};
		update cjrone_rehabilitation_subsidy set disable_id = #{disableId} where id_card = #{idCard};
	</select>
    <select id="queryListByMEntity" resultType="com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity">
		SELECT * FROM data_disability_certificate where id_card=#{idCard} and disable_id=#{disableId}
	</select>

    <select id="getByIDCard" resultType="com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity">
	    SELECT a.*,
IFNULL((SELECT medical_insurance from disability_certificate_sync_data where id_card= #{idCard}  ),'无') as health_care_condition ,
IFNULL((SELECT family_econo_condition from disability_certificate_sync_data where id_card= #{idCard} ),'无') as family_econo_condition,
IFNULL((SELECT is_fx from disability_certificate_sync_data where id_card= #{idCard} ),'无') as is_fx,
IFNULL((SELECT is_dead from disability_certificate_sync_data where id_card= #{idCard} ),'无') as is_dead
FROM data_disability_certificate a WHERE a.id_card= #{idCard} order by create_time desc limit 1 offset 0
    </select>

    <select id="querysend1" resultType="com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity">
        select * from data_disability_certificate where jiedao=#{jiedao} and mobile_phone != ''
    </select>


    <select id="querysend2" resultType="com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity">
        select * from data_disability_certificate where jiedao=#{jiedao} and mobile_phone = '' and guardian_mobile != ''
    </select>

</mapper>
