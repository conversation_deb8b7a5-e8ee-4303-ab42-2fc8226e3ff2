package com.hmit.kernespring.modules.matter_application.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.matter_application.entity.DataDisabilityCertificateImportRecordEntity;

import java.util.Map;

import java.util.List;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-09-10 15:40:23
 */
public interface DataDisabilityCertificateImportRecordService extends IService<DataDisabilityCertificateImportRecordEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataDisabilityCertificateImportRecordEntity> queryExportData(Map<String, Object> params);
}

