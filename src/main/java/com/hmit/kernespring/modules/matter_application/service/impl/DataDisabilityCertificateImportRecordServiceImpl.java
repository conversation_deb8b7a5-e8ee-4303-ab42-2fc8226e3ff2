package com.hmit.kernespring.modules.matter_application.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.matter_application.dao.DataDisabilityCertificateImportRecordDao;
import com.hmit.kernespring.modules.matter_application.entity.DataDisabilityCertificateImportRecordEntity;
import com.hmit.kernespring.modules.matter_application.service.DataDisabilityCertificateImportRecordService;


@Service("dataDisabilityCertificateImportRecordService")
public class DataDisabilityCertificateImportRecordServiceImpl extends ServiceImpl<DataDisabilityCertificateImportRecordDao, DataDisabilityCertificateImportRecordEntity> implements DataDisabilityCertificateImportRecordService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataDisabilityCertificateImportRecordDao dataDisabilityCertificateImportRecordDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataDisabilityCertificateImportRecordEntity dataDisabilityCertificateImportRecordEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataDisabilityCertificateImportRecordEntity.class);
        IPage<DataDisabilityCertificateImportRecordEntity> page = this.page(
                new Query<DataDisabilityCertificateImportRecordEntity>().getPage(params),
                new QueryWrapper<DataDisabilityCertificateImportRecordEntity>()
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getId ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getId ().toString())? dataDisabilityCertificateImportRecordEntity.getId ().toString():null),"id", dataDisabilityCertificateImportRecordEntity.getId ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getImportBatchNo ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getImportBatchNo ().toString())? dataDisabilityCertificateImportRecordEntity.getImportBatchNo ().toString():null),"import_batch_no", dataDisabilityCertificateImportRecordEntity.getImportBatchNo ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getOriginalFilename ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getOriginalFilename ().toString())? dataDisabilityCertificateImportRecordEntity.getOriginalFilename ().toString():null),"original_filename", dataDisabilityCertificateImportRecordEntity.getOriginalFilename ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getFilePath ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getFilePath ().toString())? dataDisabilityCertificateImportRecordEntity.getFilePath ().toString():null),"file_path", dataDisabilityCertificateImportRecordEntity.getFilePath ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getSysOssId ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getSysOssId ().toString())? dataDisabilityCertificateImportRecordEntity.getSysOssId ().toString():null),"sys_oss_id", dataDisabilityCertificateImportRecordEntity.getSysOssId ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getImportUserId ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getImportUserId ().toString())? dataDisabilityCertificateImportRecordEntity.getImportUserId ().toString():null),"import_user_id", dataDisabilityCertificateImportRecordEntity.getImportUserId ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getImportTime ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getImportTime ().toString())? dataDisabilityCertificateImportRecordEntity.getImportTime ().toString():null),"import_time", dataDisabilityCertificateImportRecordEntity.getImportTime ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getOriginalDataId ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getOriginalDataId ().toString())? dataDisabilityCertificateImportRecordEntity.getOriginalDataId ().toString():null),"original_data_id", dataDisabilityCertificateImportRecordEntity.getOriginalDataId ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getRowNumber ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getRowNumber ().toString())? dataDisabilityCertificateImportRecordEntity.getRowNumber ().toString():null),"row_number", dataDisabilityCertificateImportRecordEntity.getRowNumber ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getName ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getName ().toString())? dataDisabilityCertificateImportRecordEntity.getName ().toString():null),"name", dataDisabilityCertificateImportRecordEntity.getName ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getIdCard ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getIdCard ().toString())? dataDisabilityCertificateImportRecordEntity.getIdCard ().toString():null),"id_card", dataDisabilityCertificateImportRecordEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getSex ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getSex ().toString())? dataDisabilityCertificateImportRecordEntity.getSex ().toString():null),"sex", dataDisabilityCertificateImportRecordEntity.getSex ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getNationality ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getNationality ().toString())? dataDisabilityCertificateImportRecordEntity.getNationality ().toString():null),"nationality", dataDisabilityCertificateImportRecordEntity.getNationality ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getEducationDegree ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getEducationDegree ().toString())? dataDisabilityCertificateImportRecordEntity.getEducationDegree ().toString():null),"education_degree", dataDisabilityCertificateImportRecordEntity.getEducationDegree ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getMaritalStatus ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getMaritalStatus ().toString())? dataDisabilityCertificateImportRecordEntity.getMaritalStatus ().toString():null),"marital_status", dataDisabilityCertificateImportRecordEntity.getMaritalStatus ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getAccountType ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getAccountType ().toString())? dataDisabilityCertificateImportRecordEntity.getAccountType ().toString():null),"account_type", dataDisabilityCertificateImportRecordEntity.getAccountType ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getTelphone ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getTelphone ().toString())? dataDisabilityCertificateImportRecordEntity.getTelphone ().toString():null),"telphone", dataDisabilityCertificateImportRecordEntity.getTelphone ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getMobilePhone ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getMobilePhone ().toString())? dataDisabilityCertificateImportRecordEntity.getMobilePhone ().toString():null),"mobile_phone", dataDisabilityCertificateImportRecordEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getJiedao ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getJiedao ().toString())? dataDisabilityCertificateImportRecordEntity.getJiedao ().toString():null),"jiedao", dataDisabilityCertificateImportRecordEntity.getJiedao ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getShequ ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getShequ ().toString())? dataDisabilityCertificateImportRecordEntity.getShequ ().toString():null),"shequ", dataDisabilityCertificateImportRecordEntity.getShequ ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getNativePlace ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getNativePlace ().toString())? dataDisabilityCertificateImportRecordEntity.getNativePlace ().toString():null),"native_place", dataDisabilityCertificateImportRecordEntity.getNativePlace ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getLivePlace ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getLivePlace ().toString())? dataDisabilityCertificateImportRecordEntity.getLivePlace ().toString():null),"live_place", dataDisabilityCertificateImportRecordEntity.getLivePlace ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getGuardianName ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getGuardianName ().toString())? dataDisabilityCertificateImportRecordEntity.getGuardianName ().toString():null),"guardian_name", dataDisabilityCertificateImportRecordEntity.getGuardianName ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getGuardianRelation ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getGuardianRelation ().toString())? dataDisabilityCertificateImportRecordEntity.getGuardianRelation ().toString():null),"guardian_relation", dataDisabilityCertificateImportRecordEntity.getGuardianRelation ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getGuardianTelephone ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getGuardianTelephone ().toString())? dataDisabilityCertificateImportRecordEntity.getGuardianTelephone ().toString():null),"guardian_telephone", dataDisabilityCertificateImportRecordEntity.getGuardianTelephone ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getGuardianMobile ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getGuardianMobile ().toString())? dataDisabilityCertificateImportRecordEntity.getGuardianMobile ().toString():null),"guardian_mobile", dataDisabilityCertificateImportRecordEntity.getGuardianMobile ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getDisableId ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getDisableId ().toString())? dataDisabilityCertificateImportRecordEntity.getDisableId ().toString():null),"disable_id", dataDisabilityCertificateImportRecordEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getDisabilityCategory ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getDisabilityCategory ().toString())? dataDisabilityCertificateImportRecordEntity.getDisabilityCategory ().toString():null),"disability_category", dataDisabilityCertificateImportRecordEntity.getDisabilityCategory ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getDisabilityDegree ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getDisabilityDegree ().toString())? dataDisabilityCertificateImportRecordEntity.getDisabilityDegree ().toString():null),"disability_degree", dataDisabilityCertificateImportRecordEntity.getDisabilityDegree ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getDisabilityInfo ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getDisabilityInfo ().toString())? dataDisabilityCertificateImportRecordEntity.getDisabilityInfo ().toString():null),"disability_info", dataDisabilityCertificateImportRecordEntity.getDisabilityInfo ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getCompleteTime ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getCompleteTime ().toString())? dataDisabilityCertificateImportRecordEntity.getCompleteTime ().toString():null),"complete_time", dataDisabilityCertificateImportRecordEntity.getCompleteTime ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getStatus ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getStatus ().toString())? dataDisabilityCertificateImportRecordEntity.getStatus ().toString():null),"status", dataDisabilityCertificateImportRecordEntity.getStatus ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getCreateId ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getCreateId ().toString())? dataDisabilityCertificateImportRecordEntity.getCreateId ().toString():null),"create_id", dataDisabilityCertificateImportRecordEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getCreateTime ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getCreateTime ().toString())? dataDisabilityCertificateImportRecordEntity.getCreateTime ().toString():null),"create_time", dataDisabilityCertificateImportRecordEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getBankName ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getBankName ().toString())? dataDisabilityCertificateImportRecordEntity.getBankName ().toString():null),"bank_name", dataDisabilityCertificateImportRecordEntity.getBankName ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getBankAccount ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getBankAccount ().toString())? dataDisabilityCertificateImportRecordEntity.getBankAccount ().toString():null),"bank_account", dataDisabilityCertificateImportRecordEntity.getBankAccount ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getBirthday ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getBirthday ().toString())? dataDisabilityCertificateImportRecordEntity.getBirthday ().toString():null),"birthday", dataDisabilityCertificateImportRecordEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getPostalCode ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getPostalCode ().toString())? dataDisabilityCertificateImportRecordEntity.getPostalCode ().toString():null),"postal_code", dataDisabilityCertificateImportRecordEntity.getPostalCode ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getContactPersonIdCard ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getContactPersonIdCard ().toString())? dataDisabilityCertificateImportRecordEntity.getContactPersonIdCard ().toString():null),"contact_person_id_card", dataDisabilityCertificateImportRecordEntity.getContactPersonIdCard ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getMedicalInsuranceStatus ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getMedicalInsuranceStatus ().toString())? dataDisabilityCertificateImportRecordEntity.getMedicalInsuranceStatus ().toString():null),"medical_insurance_status", dataDisabilityCertificateImportRecordEntity.getMedicalInsuranceStatus ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getDiagnosisCertificateStartTime ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getDiagnosisCertificateStartTime ().toString())? dataDisabilityCertificateImportRecordEntity.getDiagnosisCertificateStartTime ().toString():null),"diagnosis_certificate_start_time", dataDisabilityCertificateImportRecordEntity.getDiagnosisCertificateStartTime ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getDiagnosisCertificateEndTime ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getDiagnosisCertificateEndTime ().toString())? dataDisabilityCertificateImportRecordEntity.getDiagnosisCertificateEndTime ().toString():null),"diagnosis_certificate_end_time", dataDisabilityCertificateImportRecordEntity.getDiagnosisCertificateEndTime ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getDiagnosisCertificateImage ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getDiagnosisCertificateImage ().toString())? dataDisabilityCertificateImportRecordEntity.getDiagnosisCertificateImage ().toString():null),"diagnosis_certificate_image", dataDisabilityCertificateImportRecordEntity.getDiagnosisCertificateImage ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getYanglaoInsuranceStatus ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getYanglaoInsuranceStatus ().toString())? dataDisabilityCertificateImportRecordEntity.getYanglaoInsuranceStatus ().toString():null),"yanglao_insurance_status", dataDisabilityCertificateImportRecordEntity.getYanglaoInsuranceStatus ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateImportRecordEntity.getFamilyEconomicCondition ()!=null && !"".equals(dataDisabilityCertificateImportRecordEntity.getFamilyEconomicCondition ().toString())? dataDisabilityCertificateImportRecordEntity.getFamilyEconomicCondition ().toString():null),"family_economic_condition", dataDisabilityCertificateImportRecordEntity.getFamilyEconomicCondition ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            SysDictEntity accountType_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("hklx_0000") && iii.getValue().equals(
                        item.getAccountType ())).findAny().orElse(null);
            if (accountType_sysDictEntity != null){
                item.setAccountType (accountType_sysDictEntity.getLabel());
            }else{
                item.setAccountType (null);
            }
            SysDictEntity disabilityCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjlb_0000") && iii.getValue().equals(
                        item.getDisabilityCategory ())).findAny().orElse(null);
            if (disabilityCategory_sysDictEntity != null){
                item.setDisabilityCategory (disabilityCategory_sysDictEntity.getLabel());
            }else{
                item.setDisabilityCategory (null);
            }
            SysDictEntity disabilityDegree_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjdj_0000") && iii.getValue().equals(
                        item.getDisabilityDegree ())).findAny().orElse(null);
            if (disabilityDegree_sysDictEntity != null){
                item.setDisabilityDegree (disabilityDegree_sysDictEntity.getLabel());
            }else{
                item.setDisabilityDegree (null);
            }
            SysDictEntity status_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("zt_0000") && iii.getValue().equals(
                        item.getStatus ())).findAny().orElse(null);
            if (status_sysDictEntity != null){
                item.setStatus (Integer.valueOf(status_sysDictEntity.getLabel()));
            }else{
                item.setStatus (null);
            }
            SysDictEntity familyEconomicCondition_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("jtjjqk_0000") && iii.getValue().equals(
                        item.getFamilyEconomicCondition ())).findAny().orElse(null);
            if (familyEconomicCondition_sysDictEntity != null){
                item.setFamilyEconomicCondition (familyEconomicCondition_sysDictEntity.getLabel());
            }else{
                item.setFamilyEconomicCondition (null);
            }
        });
        return new PageUtils(page);
    }
    @Override
    public List<DataDisabilityCertificateImportRecordEntity> queryExportData(Map<String, Object> params) {
            return dataDisabilityCertificateImportRecordDao.queryExportData(params);
    }

}