package com.hmit.kernespring.modules.matter_application.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-09-10 15:40:23
 */
@Data
@TableName("data_disability_certificate_import_record")
public class DataDisabilityCertificateImportRecordEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	
@TableId
	@Excel(name = "主键ID", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "主键ID")
private Long id;
	/**
	 * 导入批次号
	 */
	@Excel(name = "导入批次号", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "导入批次号")
private String importBatchNo;
	/**
	 * 原始文件名
	 */
	@Excel(name = "原始文件名", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "原始文件名")
private String originalFilename;
	/**
	 * 文件存储路径
	 */
	@Excel(name = "文件存储路径", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "文件存储路径")
private String filePath;
	/**
	 * 关联sys_oss表ID
	 */
	@Excel(name = "关联sys_oss表ID", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "关联sys_oss表ID")
private Long sysOssId;
	/**
	 * 导入操作人ID
	 */
	@Excel(name = "导入操作人ID", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "导入操作人ID")
private Long importUserId;
	/**
	 * 导入时间
	 */
	@Excel(name = "导入时间", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "导入时间")
private Date importTime;
	/**
	 * 对应data_disability_certificate表的ID
	 */
	@Excel(name = "对应data_disability_certificate表的ID", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "对应data_disability_certificate表的ID")
private Integer originalDataId;
	/**
	 * 在Excel中的行号
	 */
	@Excel(name = "在Excel中的行号", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "在Excel中的行号")
private Integer rowNumber;
	/**
	 * 姓名
	 */
	@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "姓名")
private String name;
	/**
	 * 身份证号
	 */
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "身份证号")
private String idCard;
	/**
	 * 性别
	 */
	@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "性别")
private String sex;
	/**
	 * 民族
	 */
	@Excel(name = "民族", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "民族")
private String nationality;
	/**
	 * 文化程度
	 */
	@Excel(name = "文化程度", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "文化程度")
private String educationDegree;
	/**
	 * 婚姻状况
	 */
	@Excel(name = "婚姻状况", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "婚姻状况")
private String maritalStatus;
	/**
	 * 户口类型
	 */
	@Excel(name = "户口类型", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "户口类型")
private String accountType;
	/**
	 * 固定电话
	 */
	@Excel(name = "固定电话", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "固定电话")
private String telphone;
	/**
	 * 手机
	 */
	@Excel(name = "手机", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "手机")
private String mobilePhone;
	/**
	 * 街道
	 */
	@Excel(name = "街道", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "街道")
private String jiedao;
	/**
	 * 社区
	 */
	@Excel(name = "社区", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "社区")
private String shequ;
	/**
	 * 户籍地址
	 */
	@Excel(name = "户籍地址", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "户籍地址")
private String nativePlace;
	/**
	 * 现居住地
	 */
	@Excel(name = "现居住地", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "现居住地")
private String livePlace;
	/**
	 * 监护人姓名
	 */
	@Excel(name = "监护人姓名", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "监护人姓名")
private String guardianName;
	/**
	 * 监护人关系
	 */
	@Excel(name = "监护人关系", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "监护人关系")
private String guardianRelation;
	/**
	 * 监护人固话
	 */
	@Excel(name = "监护人固话", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "监护人固话")
private String guardianTelephone;
	/**
	 * 监护人手机
	 */
	@Excel(name = "监护人手机", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "监护人手机")
private String guardianMobile;
	/**
	 * 残疾证号
	 */
	@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "残疾证号")
private String disableId;
	/**
	 * 残疾类别
	 */
	@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "残疾类别")
private String disabilityCategory;
	/**
	 * 残疾等级
	 */
	@Excel(name = "残疾等级", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "残疾等级")
private String disabilityDegree;
	/**
	 * 残疾详情
	 */
	@Excel(name = "残疾详情", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "残疾详情")
private String disabilityInfo;
	/**
	 * 持证时间
	 */
	@Excel(name = "持证时间", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "持证时间")
private String completeTime;
	/**
	 * 状态
	 */
	@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "状态")
private Integer status;
	/**
	 * 创建人编号
	 */
	@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "创建人编号")
private Integer createId;
	/**
	 * 创建时间
	 */
	@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "创建时间")
private Date createTime;
	/**
	 * 银行名称
	 */
	@Excel(name = "银行名称", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "银行名称")
private String bankName;
	/**
	 * 银行账户
	 */
	@Excel(name = "银行账户", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "银行账户")
private String bankAccount;
	/**
	 * 生日
	 */
	@Excel(name = "生日", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "生日")
private String birthday;
	/**
	 * 邮编
	 */
	@Excel(name = "邮编", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "邮编")
private String postalCode;
	/**
	 * 联系人身份证号
	 */
	@Excel(name = "联系人身份证号", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "联系人身份证号")
private String contactPersonIdCard;
	/**
	 * 享受医疗保险状况
	 */
	@Excel(name = "享受医疗保险状况", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "享受医疗保险状况")
private String medicalInsuranceStatus;
	/**
	 * 诊断证明起始时间
	 */
	@Excel(name = "诊断证明起始时间", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "诊断证明起始时间")
private Date diagnosisCertificateStartTime;
	/**
	 * 诊断证明结束时间
	 */
	@Excel(name = "诊断证明结束时间", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "诊断证明结束时间")
private Date diagnosisCertificateEndTime;
	/**
	 * 诊断证明照片URL
	 */
	@Excel(name = "诊断证明照片URL", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "诊断证明照片URL")
private String diagnosisCertificateImage;
	/**
	 * 享受养老保险状况
	 */
	@Excel(name = "享受养老保险状况", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "享受养老保险状况")
private String yanglaoInsuranceStatus;
	/**
	 * 家庭经济情况
	 */
	@Excel(name = "家庭经济情况", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "家庭经济情况")
private String familyEconomicCondition;

}
