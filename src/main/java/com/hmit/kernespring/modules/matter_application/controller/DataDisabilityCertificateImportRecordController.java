package com.hmit.kernespring.modules.matter_application.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.matter_application.entity.DataDisabilityCertificateImportRecordEntity;
import com.hmit.kernespring.modules.matter_application.service.DataDisabilityCertificateImportRecordService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-09-10 15:40:23
 */
@RestController
@RequestMapping("matter_application/datadisabilitycertificateimportrecord")
public class DataDisabilityCertificateImportRecordController {
    @Autowired
    private DataDisabilityCertificateImportRecordService dataDisabilityCertificateImportRecordService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
//    @RequiresPermissions("matter_application:datadisabilitycertificateimportrecord:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = dataDisabilityCertificateImportRecordService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
//    @RequiresPermissions("matter_application:datadisabilitycertificateimportrecord:info")
    public R info(@PathVariable("id") Long id){
		DataDisabilityCertificateImportRecordEntity dataDisabilityCertificateImportRecord = dataDisabilityCertificateImportRecordService.getById(id);

        return R.ok().put("dataDisabilityCertificateImportRecord", dataDisabilityCertificateImportRecord);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("matter_application:datadisabilitycertificateimportrecord:save")
    public R save(@RequestBody DataDisabilityCertificateImportRecordEntity dataDisabilityCertificateImportRecord){
		dataDisabilityCertificateImportRecordService.save(dataDisabilityCertificateImportRecord);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("matter_application:datadisabilitycertificateimportrecord:update")
    public R update(@RequestBody DataDisabilityCertificateImportRecordEntity dataDisabilityCertificateImportRecord){
		dataDisabilityCertificateImportRecordService.updateById(dataDisabilityCertificateImportRecord);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("matter_application:datadisabilitycertificateimportrecord:delete")
    public R delete(@RequestBody Long[] ids){
		dataDisabilityCertificateImportRecordService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    @RequiresPermissions("matter_application:datadisabilitycertificateimportrecord:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DataDisabilityCertificateImportRecordEntity> dataDisabilityCertificateImportRecordList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get("主键ID"));
                    item.put("importBatchNo",item.get("导入批次号"));
                    item.put("originalFilename",item.get("原始文件名"));
                    item.put("filePath",item.get("文件存储路径"));
                    item.put("sysOssId",item.get("关联sys_oss表ID"));
                    item.put("importUserId",item.get("导入操作人ID"));
                    item.put("importTime",item.get("导入时间"));
                    item.put("originalDataId",item.get("对应data_disability_certificate表的ID"));
                    item.put("rowNumber",item.get("在Excel中的行号"));
                    item.put("name",item.get("姓名"));
                    item.put("idCard",item.get("身份证号"));
                    item.put("sex",item.get("性别"));
                    item.put("nationality",item.get("民族"));
                    item.put("educationDegree",item.get("文化程度"));
                    item.put("maritalStatus",item.get("婚姻状况"));
                    item.put("accountType",item.get("户口类型"));
                    item.put("telphone",item.get("固定电话"));
                    item.put("mobilePhone",item.get("手机"));
                    item.put("jiedao",item.get("街道"));
                    item.put("shequ",item.get("社区"));
                    item.put("nativePlace",item.get("户籍地址"));
                    item.put("livePlace",item.get("现居住地"));
                    item.put("guardianName",item.get("监护人姓名"));
                    item.put("guardianRelation",item.get("监护人关系"));
                    item.put("guardianTelephone",item.get("监护人固话"));
                    item.put("guardianMobile",item.get("监护人手机"));
                    item.put("disableId",item.get("残疾证号"));
                    item.put("disabilityCategory",item.get("残疾类别"));
                    item.put("disabilityDegree",item.get("残疾等级"));
                    item.put("disabilityInfo",item.get("残疾详情"));
                    item.put("completeTime",item.get("持证时间"));
                    item.put("status",item.get("状态"));
                    item.put("createId",item.get("创建人编号"));
                    item.put("createTime",item.get("创建时间"));
                    item.put("bankName",item.get("银行名称"));
                    item.put("bankAccount",item.get("银行账户"));
                    item.put("birthday",item.get("生日"));
                    item.put("postalCode",item.get("邮编"));
                    item.put("contactPersonIdCard",item.get("联系人身份证号"));
                    item.put("medicalInsuranceStatus",item.get("享受医疗保险状况"));
                    item.put("diagnosisCertificateStartTime",item.get("诊断证明起始时间"));
                    item.put("diagnosisCertificateEndTime",item.get("诊断证明结束时间"));
                    item.put("diagnosisCertificateImage",item.get("诊断证明照片URL"));
                    item.put("yanglaoInsuranceStatus",item.get("享受养老保险状况"));
                    item.put("familyEconomicCondition",item.get("家庭经济情况"));
                    dataDisabilityCertificateImportRecordList.add(new Gson().fromJson(new Gson().toJson(item), DataDisabilityCertificateImportRecordEntity.class));
        });
        // 保存到数据库
        dataDisabilityCertificateImportRecordService.saveBatch(dataDisabilityCertificateImportRecordList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    @RequiresPermissions("matter_application:datadisabilitycertificateimportrecord:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DataDisabilityCertificateImportRecordEntity> dataDisabilityCertificateImportRecordEntityList = dataDisabilityCertificateImportRecordService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("${comments}", null, "${comments}");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DataDisabilityCertificateImportRecordEntity.class, dataDisabilityCertificateImportRecordEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "${comments}" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
