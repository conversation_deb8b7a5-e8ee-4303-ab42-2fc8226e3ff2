package com.hmit.kernespring.modules.matter_application.dao;

import com.hmit.kernespring.modules.matter_application.entity.DataDisabilityCertificateImportRecordEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-09-10 15:40:23
 */
@Mapper
public interface DataDisabilityCertificateImportRecordDao extends BaseMapper<DataDisabilityCertificateImportRecordEntity> {
    List<DataDisabilityCertificateImportRecordEntity> queryExportData(Map<String, Object> params);
	
}
