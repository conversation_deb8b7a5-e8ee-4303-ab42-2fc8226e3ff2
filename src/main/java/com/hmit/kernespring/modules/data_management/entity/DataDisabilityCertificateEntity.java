package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 残疾人汇总表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-11-23 17:09:07
 */
@Data
@TableName("data_disability_certificate")
public class DataDisabilityCertificateEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 姓名
	 */
@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号
	 */
@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 性别
	 */
@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 民族
	 */
@Excel(name = "民族", height = 20, width = 30, isImportField = "true_st")
private String nationality;
	/**
	 * 文化程度
	 */
@Excel(name = "文化程度", height = 20, width = 30, isImportField = "true_st")
private String educationDegree;
	/**
	 * 婚姻状况
	 */
@Excel(name = "婚姻状况", height = 20, width = 30, isImportField = "true_st")
private String maritalStatus;
	/**
	 * 户口类型
	 */
@Excel(name = "户口类型", height = 20, width = 30, isImportField = "true_st")
private String accountType;
	/**
	 * 固定电话
	 */
@Excel(name = "固定电话", height = 20, width = 30, isImportField = "true_st")
private String telphone;
	/**
	 * 手机
	 */
@Excel(name = "手机", height = 20, width = 30, isImportField = "true_st")
private String mobilePhone;
	/**
	 * 街道
	 */
@Excel(name = "街道", height = 20, width = 30, isImportField = "true_st")
private String jiedao;
	/**
	 * 社区
	 */
@Excel(name = "社区", height = 20, width = 30, isImportField = "true_st")
private String shequ;
	/**
	 * 户籍地址
	 */
@Excel(name = "户籍地址", height = 20, width = 30, isImportField = "true_st")
private String nativePlace;
	/**
	 * 现居住地
	 */
@Excel(name = "现居住地", height = 20, width = 30, isImportField = "true_st")
private String livePlace;
	/**
	 * 监护人姓名
	 */
@Excel(name = "监护人姓名", height = 20, width = 30, isImportField = "true_st")
private String guardianName;
	/**
	 * 监护人关系
	 */
@Excel(name = "监护人关系", height = 20, width = 30, isImportField = "true_st")
private String guardianRelation;
	/**
	 * 监护人固话
	 */
@Excel(name = "监护人固话", height = 20, width = 30, isImportField = "true_st")
private String guardianTelephone;
	/**
	 * 监护人手机
	 */
@Excel(name = "监护人手机", height = 20, width = 30, isImportField = "true_st")
private String guardianMobile;
	/**
	 * 残疾证号
	 */
@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 残疾类别
	 */
@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
private String disabilityCategory;
	/**
	 * 残疾等级
	 */
@Excel(name = "残疾等级", height = 20, width = 30, isImportField = "true_st")
private String disabilityDegree;
	/**
	 * 残疾详情
	 */
@Excel(name = "残疾详情", height = 20, width = 30, isImportField = "true_st")
private String disabilityInfo;
	/**
	 * 持证时间
	 */
@Excel(name = "持证时间", height = 20, width = 30, isImportField = "true_st")
private String completeTime;
	/**
	 * 状态
	 */
//@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
private Integer status;
	/**
	 * 创建人编号
	 */
@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")
private Integer createId;
	/**
	 * 创建时间
	 */
@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private Date createTime;

private String bankName;

private String bankAccount;

private String birthday;

	@TableField(exist = false)
	private String isFx;
	@TableField(exist = false)
	private String isDead;
	@TableField(exist =  false)
	private String healthCareCondition;
	@TableField(exist =  false)
	private String familyEconoCondition;

	/**
	 * 邮编
	 */
	@Excel(name = "邮编", height = 20, width = 30, isImportField = "true_st")
	private String postalCode;
	/**
	 * 联系人身份证号
	 */
	@Excel(name = "联系人身份证号", height = 20, width = 30, isImportField = "true_st")
	private String contactPersonIdCard;
	/**
	 * 享受医疗保险状况
	 */
	@Excel(name = "享受医疗保险状况", height = 20, width = 30, isImportField = "true_st")
	private String medicalInsuranceStatus;

	//诊断证明时间 起始时间
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date diagnosisCertificateStartTime;

	//诊断证明时间 结束时间
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date diagnosisCertificateEndTime;


	//诊断证明照片URL
	private String diagnosisCertificateImage;

	//享受养老保险状况
	private String yanglaoInsuranceStatus;

	//家庭经济情况
	private String familyEconomicCondition;

}
