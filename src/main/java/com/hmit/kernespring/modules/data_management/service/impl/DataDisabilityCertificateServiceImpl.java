package com.hmit.kernespring.modules.data_management.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.data_management.dao.DataDisabilityCertificateDao;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service("dataDisabilityCertificateService")
public class DataDisabilityCertificateServiceImpl extends ServiceImpl<DataDisabilityCertificateDao, DataDisabilityCertificateEntity> implements DataDisabilityCertificateService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataDisabilityCertificateDao dataDisabilityCertificateDao;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;

    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataDisabilityCertificateEntity dataDisabilityCertificateEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataDisabilityCertificateEntity.class);
        IPage<DataDisabilityCertificateEntity> page = this.page(
                new Query<DataDisabilityCertificateEntity>().getPage(params),
                new QueryWrapper<DataDisabilityCertificateEntity>()
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateEntity.getId ()!=null && !"".equals(dataDisabilityCertificateEntity.getId ().toString())? dataDisabilityCertificateEntity.getId ().toString():null),"id", dataDisabilityCertificateEntity.getId ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateEntity.getName ()!=null && !"".equals(dataDisabilityCertificateEntity.getName ().toString())? dataDisabilityCertificateEntity.getName ().toString():null),"name", dataDisabilityCertificateEntity.getName ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateEntity.getIdCard ()!=null && !"".equals(dataDisabilityCertificateEntity.getIdCard ().toString())? dataDisabilityCertificateEntity.getIdCard ().toString():null),"id_card", dataDisabilityCertificateEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateEntity.getDisableId ()!=null && !"".equals(dataDisabilityCertificateEntity.getDisableId ().toString())? dataDisabilityCertificateEntity.getDisableId ().toString():null),"disable_id", dataDisabilityCertificateEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateEntity.getSex ()!=null && !"".equals(dataDisabilityCertificateEntity.getSex ().toString())? dataDisabilityCertificateEntity.getSex ().toString():null),"sex", dataDisabilityCertificateEntity.getSex ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateEntity.getDisabilityCategory ()!=null && !"".equals(dataDisabilityCertificateEntity.getDisabilityCategory ().toString())? dataDisabilityCertificateEntity.getDisabilityCategory ().toString():null),"disability_category", dataDisabilityCertificateEntity.getDisabilityCategory ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateEntity.getDisabilityDegree ()!=null && !"".equals(dataDisabilityCertificateEntity.getDisabilityDegree ().toString())? dataDisabilityCertificateEntity.getDisabilityDegree ().toString():null),"disability_degree", dataDisabilityCertificateEntity.getDisabilityDegree ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateEntity.getGuardianName ()!=null && !"".equals(dataDisabilityCertificateEntity.getGuardianName ().toString())? dataDisabilityCertificateEntity.getGuardianName ().toString():null),"guardian_name", dataDisabilityCertificateEntity.getGuardianName ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateEntity.getStatus ()!=null && !"".equals(dataDisabilityCertificateEntity.getStatus ().toString())? dataDisabilityCertificateEntity.getStatus ().toString():null),"status", dataDisabilityCertificateEntity.getStatus ())
            .eq(StringUtils.isNotBlank(dataDisabilityCertificateEntity.getCreateId ()!=null && !"".equals(dataDisabilityCertificateEntity.getCreateId ().toString())? dataDisabilityCertificateEntity.getCreateId ().toString():null),"create_id", dataDisabilityCertificateEntity.getCreateId ())
            );

        return new PageUtils(page);
    }

    @Override
    public List<DataDisabilityCertificateEntity> queryExportData(Map<String, Object> params) {
            return dataDisabilityCertificateDao.queryExportData(params);
    }

    @Override
    public void updateOthersDisableIdByMap(Map<String, Object> params) {
        dataDisabilityCertificateDao.updateOthersDisableIdByMap(params);
    }

    @Override
    public List<DataDisabilityCertificateEntity> queryListByMEntity(DataDisabilityCertificateEntity dataDisabilityCertificateEntity) {
        return dataDisabilityCertificateDao.queryListByMEntity(dataDisabilityCertificateEntity);
    }

    @Override
    public DataDisabilityCertificateEntity getByIDCard(String idCard) {
        return dataDisabilityCertificateDao.getByIDCard(idCard);
    }

    @Override
    @Transactional
    public boolean saveOrUpdate(DataDisabilityCertificateEntity entity) {
        if (entity.getDisableId() != null && entity.getIdCard() != null) {
            Map<String, Object> nmap = new HashMap<>();
            nmap.put("disableId", entity.getDisableId());
            nmap.put("idCard", entity.getIdCard());
            System.out.println(nmap);
            dataDisabilityCertificateDao.updateOthersADisableIdByMap(nmap);
//            dataDisabilityCertificateDao.updateOthersBDisableIdByMap(nmap);
            dataDisabilityCertificateDao.updateOthersCDisableIdByMap(nmap);
            dataDisabilityCertificateDao.updateOthersDDisableIdByMap(nmap);
            // dataDisabilityCertificateDao.updateOthersDisableIdByMap(nmap);
        }
        return super.saveOrUpdate(entity);
    }
}
